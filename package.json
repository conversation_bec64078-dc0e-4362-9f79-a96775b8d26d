{"name": "economy-bot", "version": "2.0.0", "main": "dist/main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "build:watch": "tsc --watch", "start": "node dist/main.js", "start:legacy": "node dist/index.js", "dev": "ts-node src/main.ts", "dev:legacy": "ts-node src/index.ts", "deploy-commands": "ts-node src/deploy-commands.ts", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts", "format": "prettier --write src/**/*.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@discordjs/rest": "^2.5.0", "discord-api-types": "^0.38.8", "discord.js": "^14.19.3", "dotenv": "^16.6.1", "mongodb": "^6.17.0", "mongoose": "^8.15.0", "node-cron": "^4.0.7", "winston": "^3.17.0"}, "devDependencies": {"nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}