/**
 * Interaction Create Event Handler
 * Handles Discord interaction events (commands and buttons)
 */

import { Interaction, ChatInputCommandInteraction, ButtonInteraction } from 'discord.js';
import { BaseEventHandler } from './base';
import { IApplicationContext, CommandContext, ButtonContext } from '../core/interfaces';
import { handleCommandError, handleButtonError } from '../utils/errorHandler';
import { EMOJIS } from '../utils/embedBuilder';

/**
 * Interaction create event handler
 */
export class InteractionCreateEventHandler extends BaseEventHandler {
  public readonly name = 'interactionCreate';

  constructor(app: IApplicationContext) {
    super(app, 'interactionCreate');
  }

  /**
   * Execute interaction create event
   */
  async execute(interaction: Interaction): Promise<void> {
    try {
      if (interaction.isChatInputCommand()) {
        await this.handleChatInputCommand(interaction);
      } else if (interaction.isButton()) {
        await this.handleButtonInteraction(interaction);
      }
    } catch (error) {
      this.handleError(error, {
        interactionType: interaction.type,
        userId: interaction.user.id,
        guildId: interaction.guild?.id,
      });
    }
  }

  /**
   * Handle chat input command interactions
   */
  private async handleChatInputCommand(interaction: ChatInputCommandInteraction): Promise<void> {
    const command = (this.app.client as any).commands.get(interaction.commandName);
    if (!command) {
      this.logger.warn(`[InteractionCreate] Unknown command: ${interaction.commandName}`);
      return;
    }

    try {
      this.logExecution(`Executing command: ${interaction.commandName}`, {
        userId: interaction.user.id,
        guildId: interaction.guild?.id,
        channelId: interaction.channel?.id,
      });

      // Create command context
      const context: CommandContext = {
        interaction,
        client: this.app.client,
        guild: interaction.guild,
        member: interaction.member as any,
        logger: this.logger,
      };

      await command.execute(interaction);
    } catch (error) {
      await handleCommandError(interaction, error);
    }
  }

  /**
   * Handle button interactions
   */
  private async handleButtonInteraction(interaction: ButtonInteraction): Promise<void> {
    try {
      const { customId } = interaction;

      this.logExecution(`Handling button interaction: ${customId}`, {
        userId: interaction.user.id,
        guildId: interaction.guild?.id,
        channelId: interaction.channel?.id,
      });

      // Create button context
      const context: ButtonContext = {
        interaction,
        client: this.app.client,
        guild: interaction.guild,
        member: interaction.member as any,
        logger: this.logger,
      };

      // Handle different button types
      if (customId === 'quick_balance') {
        await this.handleQuickAction(interaction, 'balance');
      } else if (customId === 'quick_leaderboard') {
        await this.handleQuickAction(interaction, 'leaderboard');
      } else if (customId === 'quick_roles') {
        await this.handleQuickAction(interaction, 'roles');
      } else if (customId.startsWith('buy_role_')) {
        await this.handleRoleAchievementInfo(interaction, customId);
      } else if (customId === 'announce_confirm') {
        await this.handleAnnouncementConfirm(interaction);
      } else if (customId === 'announce_cancel') {
        await this.handleAnnouncementCancel(interaction);
      } else if (customId.startsWith('help_')) {
        await this.handleHelpButton(interaction, customId);
      } else {
        await interaction.reply({
          content: 'This button interaction is not yet implemented.',
          ephemeral: true
        });
      }
    } catch (error) {
      await handleButtonError(interaction, error);
    }
  }

  /**
   * Handle quick action buttons
   */
  private async handleQuickAction(interaction: ButtonInteraction, commandName: string): Promise<void> {
    const command = (this.app.client as any).commands.get(commandName);
    if (command) {
      await command.execute(interaction);
    } else {
      await interaction.reply({
        content: `Command "${commandName}" not found.`,
        ephemeral: true
      });
    }
  }

  /**
   * Handle role achievement info buttons
   */
  private async handleRoleAchievementInfo(interaction: ButtonInteraction, customId: string): Promise<void> {
    const roleId = customId.replace('buy_role_', '');
    await interaction.reply({
      content: `Role achievements are automatically unlocked when you reach the required PLC balance! Keep earning coins to unlock this achievement.`,
      ephemeral: true
    });
  }

  /**
   * Handle announcement confirmation
   */
  private async handleAnnouncementConfirm(interaction: ButtonInteraction): Promise<void> {
    const pendingAnnouncements = (global as any).pendingAnnouncements;
    if (!pendingAnnouncements) {
      await interaction.reply({
        content: 'No pending announcements found. Please try the command again.',
        ephemeral: true
      });
      return;
    }

    const originalInteractionId = interaction.message?.interaction?.id;
    const announcementData = pendingAnnouncements.get(originalInteractionId);

    if (!announcementData) {
      await interaction.reply({
        content: 'Announcement data not found or expired. Please try the command again.',
        ephemeral: true
      });
      return;
    }

    // Clean up the pending announcement
    pendingAnnouncements.delete(originalInteractionId);

    // Process the announcement
    await interaction.deferUpdate();
    const announceModule = require('../commands/announce');
    await announceModule.processAnnouncement(interaction, announcementData);
  }

  /**
   * Handle announcement cancellation
   */
  private async handleAnnouncementCancel(interaction: ButtonInteraction): Promise<void> {
    const pendingAnnouncements = (global as any).pendingAnnouncements;
    const originalInteractionId = interaction.message?.interaction?.id;

    if (pendingAnnouncements && originalInteractionId) {
      pendingAnnouncements.delete(originalInteractionId);
    }

    await interaction.update({
      content: `${EMOJIS.ADMIN.WARNING} Announcement cancelled.`,
      embeds: [],
      components: []
    });
  }

  /**
   * Handle help command buttons
   */
  private async handleHelpButton(interaction: ButtonInteraction, customId: string): Promise<void> {
    const commandName = customId.replace('help_', '');

    // Commands that can be executed directly
    const instantTriggerCommands = ['balance', 'roles', 'leaderboard', 'history'];

    if (instantTriggerCommands.includes(commandName)) {
      // Execute the command directly
      const command = (this.app.client as any).commands.get(commandName);
      if (command) {
        await command.execute(interaction);
      } else {
        await interaction.reply({
          content: `Command "${commandName}" not found.`,
          ephemeral: true
        });
      }
    } else {
      // For other commands, show usage information
      const commandDescriptions: Record<string, string> = {
        'pay': 'Transfer coins to another user. Usage: `/pay @user amount`',
        'addrole': '**[Admin Only]** Add a role achievement. Usage: `/addrole @role price`',
        'editrole': '**[Admin Only]** Edit a role achievement. Usage: `/editrole role_name new_price`',
        'removerole': '**[Admin Only]** Remove a role achievement. Usage: `/removerole role_name`',
        'give': '**[Admin Only]** Give coins to a user. Usage: `/give @user amount`',
        'fine': '**[Admin Only]** Remove coins from a user. Usage: `/fine @user amount`',
        'tax': '**[Admin Only]** Configure automatic taxation system. Usage: `/tax status:on/off frequency:weeks amount:plc role:@role`',
        'starterbalance': '**[Admin Only]** Manage starter balance rules. Usage: `/starterbalance action:add/edit/remove/list role:@role amount:plc`',
        'incomecredentials': '**[Admin Only]** Customize income earning guide text displayed in /help command. Supports line breaks (\\n) and formatting. Usage: `/incomecredentials text:"Your custom income guide text"`',
        'automessage': '**[Admin Only]** Manage automated messages for server events. Usage: `/automessage action:create trigger:member_join delivery:channel name:welcome title:Welcome! description:Hello {user}!`',
        'placeholders': 'View available placeholders for automated messages. Usage: `/placeholders`',
        'testcleanup': '**[Admin Only]** Test user data cleanup functionality. Usage: `/testcleanup user:@user action:check/simulate`'
      };

      const description = commandDescriptions[commandName] || `Use the /${commandName} command.`;

      await interaction.reply({
        content: `**/${commandName}**\n${description}`,
        ephemeral: true
      });
    }
  }
}
