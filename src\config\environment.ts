/**
 * Environment Configuration
 * Handles environment variables with validation and type safety
 */

import dotenv from 'dotenv';
import { ValidationError } from '../utils/errorHandler';

// Load environment variables
dotenv.config();

/**
 * Environment variable schema with validation
 */
interface EnvironmentConfig {
  // Discord Configuration
  BOT_TOKEN: string;
  CLIENT_ID: string;
  
  // Database Configuration
  MONGODB_URI: string;
  
  // Application Configuration
  NODE_ENV: 'development' | 'production' | 'test';
  PORT: number;
  
  // Feature Toggles (optional)
  ENABLE_DYNASTY_SYSTEM?: boolean;
  ENABLE_MILESTONE_SYSTEM?: boolean;
  ENABLE_REACTION_REWARDS?: boolean;
  ENABLE_TAX_SYSTEM?: boolean;
  
  // Logging Configuration
  LOG_LEVEL?: 'error' | 'warn' | 'info' | 'debug';
  LOG_FILE_PATH?: string;
  
  // Development Configuration
  DEBUG_MODE?: boolean;
  VERBOSE_LOGGING?: boolean;
}

/**
 * Validates and parses environment variables
 */
function validateEnvironment(): EnvironmentConfig {
  const errors: string[] = [];
  
  // Required variables
  const BOT_TOKEN = process.env.BOT_TOKEN;
  if (!BOT_TOKEN) {
    errors.push('BOT_TOKEN is required');
  }
  
  const CLIENT_ID = process.env.CLIENT_ID;
  if (!CLIENT_ID) {
    errors.push('CLIENT_ID is required');
  }
  
  const MONGODB_URI = process.env.MONGODB_URI;
  if (!MONGODB_URI) {
    errors.push('MONGODB_URI is required');
  }
  
  // Node environment
  const NODE_ENV = process.env.NODE_ENV as 'development' | 'production' | 'test';
  if (!NODE_ENV || !['development', 'production', 'test'].includes(NODE_ENV)) {
    errors.push('NODE_ENV must be one of: development, production, test');
  }
  
  // Port
  const PORT = parseInt(process.env.PORT || '3000', 10);
  if (isNaN(PORT) || PORT < 1 || PORT > 65535) {
    errors.push('PORT must be a valid port number (1-65535)');
  }
  
  if (errors.length > 0) {
    throw new ValidationError(`Environment validation failed: ${errors.join(', ')}`);
  }
  
  return {
    BOT_TOKEN: BOT_TOKEN!,
    CLIENT_ID: CLIENT_ID!,
    MONGODB_URI: MONGODB_URI!,
    NODE_ENV: NODE_ENV!,
    PORT,
    
    // Optional feature toggles
    ENABLE_DYNASTY_SYSTEM: process.env.ENABLE_DYNASTY_SYSTEM === 'true',
    ENABLE_MILESTONE_SYSTEM: process.env.ENABLE_MILESTONE_SYSTEM !== 'false', // Default true
    ENABLE_REACTION_REWARDS: process.env.ENABLE_REACTION_REWARDS !== 'false', // Default true
    ENABLE_TAX_SYSTEM: process.env.ENABLE_TAX_SYSTEM !== 'false', // Default true
    
    // Logging
    LOG_LEVEL: (process.env.LOG_LEVEL as any) || 'info',
    LOG_FILE_PATH: process.env.LOG_FILE_PATH,
    
    // Development
    DEBUG_MODE: process.env.DEBUG_MODE === 'true',
    VERBOSE_LOGGING: process.env.VERBOSE_LOGGING === 'true',
  };
}

/**
 * Validated environment configuration
 */
export const ENV = validateEnvironment();

/**
 * Helper functions for environment checks
 */
export const isDevelopment = () => ENV.NODE_ENV === 'development';
export const isProduction = () => ENV.NODE_ENV === 'production';
export const isTest = () => ENV.NODE_ENV === 'test';

/**
 * Feature flag helpers
 */
export const isFeatureEnabled = (feature: keyof typeof ENV): boolean => {
  const value = ENV[feature];
  return typeof value === 'boolean' ? value : false;
};

/**
 * Database configuration helper
 */
export const getDatabaseConfig = () => ({
  uri: ENV.MONGODB_URI,
  options: {
    maxPoolSize: isProduction() ? 10 : 5,
    serverSelectionTimeoutMS: 30000,
    socketTimeoutMS: 45000,
    // Removed deprecated options that are not supported in newer MongoDB driver versions:
    // bufferMaxEntries: 0,  // Not supported in MongoDB driver 4.0+
    // bufferCommands: false, // Not supported in MongoDB driver 4.0+
  },
});

/**
 * Discord configuration helper
 */
export const getDiscordConfig = () => ({
  token: ENV.BOT_TOKEN,
  clientId: ENV.CLIENT_ID,
  intents: [
    'Guilds',
    'GuildMessages',
    'MessageContent', 
    'GuildMembers',
    'GuildMessageReactions'
  ],
});

/**
 * Logging configuration helper
 */
import { LoggingConfig } from '../core/interfaces';
export const getLoggingConfig = (): LoggingConfig => ({
  level: ENV.LOG_LEVEL || 'info',
  filePath: ENV.LOG_FILE_PATH,
  console: isDevelopment(),
  file: isProduction(),
  format: isProduction() ? 'json' : 'simple',
});

/**
 * Export environment for external use
 */
export default ENV;
