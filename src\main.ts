
// Ensure .env variables are loaded before anything else
import 'dotenv/config';
/**
 * Main Application Entry Point
 * Refactored main entry point using the new modular architecture
 */

import { startApplication } from './core/application';
import { EventManager } from './events';
import { EconomyService } from './services/economy/EconomyService';
import { RoleService } from './services/role/RoleService';
import { getLogger } from './core/logger';
import fs from 'fs';
import path from 'path';

/**
 * Main application startup function
 */
async function main(): Promise<void> {
  // Prevent the bot from doing anything for the first 10 seconds after startup
  // This helps ensure all services (especially DB) are ready before handling commands/events
  const startupLogger = getLogger();
  startupLogger.info('[Main] Waiting 10 seconds before allowing any bot actions (startup delay)...');
  await new Promise(resolve => setTimeout(resolve, 10000));
  startupLogger.info('[Main] Startup delay complete. Continuing with initialization.');

  // Print all environment variables for debugging (especially on Discloud)
  // Remove this in production!
  // eslint-disable-next-line no-console
  console.log('[DEBUG] process.env:', process.env);

  // Check for required environment variables
  const requiredEnv = ['BOT_TOKEN', 'MONGODB_URI'];
  const missingEnv = requiredEnv.filter((key) => !process.env[key]);
  if (missingEnv.length > 0) {
    // eslint-disable-next-line no-console
    console.error('[ENV][Missing] The following required environment variables are missing:', missingEnv);
    throw new Error(`Missing required environment variables: ${missingEnv.join(', ')}`);
  }

  const logger = getLogger();

  try {
    logger.info('[Main] Starting Economy Bot with refactored architecture...');

    // Initialize the application
    const app = await startApplication();

    // Register additional services
    await registerServices(app);

    // Load and register commands
    await loadCommands(app);

    // Initialize event handlers
    const eventManager = new EventManager(app);
    eventManager.initialize();

    logger.info('[Main] Economy Bot started successfully!');
    logger.info(`[Main] Bot is ready as ${app.client.user?.tag}`);

  } catch (error) {
    // Print any startup error directly to the console for visibility
    // eslint-disable-next-line no-console
    console.error('[MAIN][Raw Error]', error);
    logger.error('[Main] Failed to start application', { error });
    process.exit(1);
  }
}

/**
 * Register additional services
 */
async function registerServices(app: any): Promise<void> {
  const logger = getLogger();
  
  try {
    // Register economy service
    const economyService = new EconomyService(app);
    app.registerService(economyService, { 
      autoStart: true, 
      dependencies: ['DatabaseService'],
      priority: 2 
    });

    // Register role service
    const roleService = new RoleService(app);
    app.registerService(roleService, { 
      autoStart: true, 
      dependencies: ['DatabaseService', 'EconomyService'],
      priority: 3 
    });

    logger.info('[Main] Additional services registered');
  } catch (error) {
    logger.error('[Main] Failed to register services', { error });
    throw error;
  }
}

/**
 * Load and register commands
 */
async function loadCommands(app: any): Promise<void> {
  const logger = getLogger();

  try {
    // Initialize commands collection if it doesn't exist
    if (!(app.client as any).commands) {
      (app.client as any).commands = new Map();
    }

    let loadedCommands = 0;

    // Load legacy commands (individual files)
    const commandsPath = path.join(__dirname, 'commands');
    const commandFiles = fs.readdirSync(commandsPath).filter(file =>
      (file.endsWith('.js') || file.endsWith('.ts')) &&
      !file.endsWith('.d.ts') &&
      !file.includes('index') &&
      !file.includes('Manager') &&
      !file.includes('Base')
    );

    // Skip files that are handled by new architecture
    // NOTE: Only skip commands that are confirmed to be working in new architecture
    const skipFiles = new Set([
      'enhancerole.js', 'enhancerole.ts',
      'updatenames.js', 'updatenames.ts'
      // Temporarily removed balance, pay, give to ensure legacy versions work
      // Will re-add once new architecture versions are confirmed working
    ]);

    for (const file of commandFiles) {
      if (skipFiles.has(file)) {
        logger.debug(`[Main] Skipping ${file} (handled by new architecture)`);
        continue;
      }

      try {
        const filePath = path.join(commandsPath, file);
        const command = require(filePath);

        if (command.data && command.execute) {
          (app.client as any).commands.set(command.data.name, command);
          loadedCommands++;
          logger.debug(`[Main] Loaded legacy command: ${command.data.name}`);
        } else {
          logger.warn(`[Main] Invalid command file: ${file}`);
        }
      } catch (error) {
        logger.error(`[Main] Failed to load command file: ${file}`, { error });
      }
    }

    // Load new architecture commands
    try {
      const { commandManager } = require('./commands/CommandManager');
      const stats = await commandManager.loadCommands();

      const newCommands = commandManager.getDiscordCommands();
      for (const [name, command] of newCommands) {
        // Check if legacy command already exists
        if ((app.client as any).commands.has(name)) {
          logger.warn(`[Main] Skipping new architecture command '${name}' - legacy version already loaded`);
          continue;
        }

        (app.client as any).commands.set(name, command);
        loadedCommands++;
        logger.debug(`[Main] Loaded new architecture command: ${name}`);
      }

      logger.info(`[Main] CommandManager loaded ${stats.newArchitecture} new architecture commands`);
    } catch (error) {
      logger.error('[Main] Failed to load new architecture commands', { error });
    }

    logger.info(`[Main] Loaded ${loadedCommands} commands`);
  } catch (error) {
    logger.error('[Main] Failed to load commands', { error });
    throw error;
  }
}

/**
 * Handle uncaught exceptions and rejections
 */
process.on('uncaughtException', (error) => {
  const logger = getLogger();
  logger.error('[Main] Uncaught exception', { error });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  const logger = getLogger();
  logger.error('[Main] Unhandled rejection', { reason, promise });
});

// Start the application
if (require.main === module) {
  main().catch((error) => {
    // Print any error directly to the console for maximum visibility
    // eslint-disable-next-line no-console
    console.error('[MAIN][Startup Error]', error);
    if (error && error.stack) {
      // eslint-disable-next-line no-console
      console.error('[MAIN][Startup Error Stack]', error.stack);
    }
    process.exit(1);
  });
}

export default main;
