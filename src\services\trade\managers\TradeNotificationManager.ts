/**
 * Trade Notification Manager
 * Handles Discord notifications for trade events
 */

import { Client, User as DiscordUser, TextChannel, DMChannel } from 'discord.js';
import { BaseService } from '../../base/BaseService';
import { ITrade } from '../../../models';
import {
  createTradeProposalEmbed,
  createTradeProposalButtons,
  createActiveTradeEmbed,
  createActiveTradeButtons,
  createCompletedTradeEmbed,
  createCancelledTradeEmbed
} from '../../../utils/tradeEmbedBuilder';
import { createSuccessEmbed, createInfoEmbed, EMOJIS } from '../../../utils/embedBuilder';

/**
 * Trade Notification Manager Class
 */
export class TradeNotificationManager extends BaseService {
  constructor(app: any) {
    super('TradeNotificationManager', app);
  }

  /**
   * Initialize the notification manager
   */
  async initialize(): Promise<void> {
    this.logger.info('[TradeNotificationManager] Trade notification manager initialized');
  }

  /**
   * Send trade proposal notification
   */
  async sendTradeProposal(trade: ITrade, client: Client): Promise<void> {
    this.logOperation('Sending trade proposal notification', { tradeId: trade.tradeId });

    try {
      const seller = await client.users.fetch(trade.sellerId);
      const buyer = await client.users.fetch(trade.buyerId);

      const embed = createTradeProposalEmbed(trade, seller, buyer);
      const buttons = createTradeProposalButtons(trade.tradeId);

      // Determine who to notify (the recipient of the proposal)
      const recipient = trade.initiatedBy === 'SELLER' ? buyer : seller;
      const initiator = trade.initiatedBy === 'SELLER' ? seller : buyer;

      // Send DM to recipient
      try {
        const dmChannel = await recipient.createDM();
        await dmChannel.send({
          content: `${EMOJIS.TRADE.PROPOSAL} **New Trade Proposal**\n\n${initiator.displayName} has sent you a trade proposal!`,
          embeds: [embed],
          components: [buttons]
        });

        this.logOperation('Trade proposal DM sent', {
          tradeId: trade.tradeId,
          recipient: recipient.id
        });
      } catch (dmError) {
        this.logger.warn('Could not send trade proposal DM', {
          tradeId: trade.tradeId,
          recipient: recipient.id,
          error: dmError
        });
      }

      // Also send to guild channel if possible
      try {
        const guild = await client.guilds.fetch(trade.guildId);
        // You might want to configure a specific trade channel
        // For now, we'll skip guild notifications to avoid spam
      } catch (guildError) {
        this.logger.warn('Could not access guild for trade notification', {
          tradeId: trade.tradeId,
          guildId: trade.guildId,
          error: guildError
        });
      }

    } catch (error) {
      this.handleError(error, { operation: 'send_trade_proposal', tradeId: trade.tradeId });
    }
  }

  /**
   * Send trade accepted notification
   */
  async sendTradeAccepted(trade: ITrade, client: Client): Promise<void> {
    this.logOperation('Sending trade accepted notification', { tradeId: trade.tradeId });

    try {
      const seller = await client.users.fetch(trade.sellerId);
      const buyer = await client.users.fetch(trade.buyerId);

      const embed = createActiveTradeEmbed(trade, seller, buyer);

      // Send to both parties
      const parties = [seller, buyer];

      for (const user of parties) {
        try {
          const userConfirmed = (user.id === trade.sellerId && trade.sellerConfirmed) ||
                               (user.id === trade.buyerId && trade.buyerConfirmed);
          const buttons = createActiveTradeButtons(trade.tradeId, userConfirmed);

          const dmChannel = await user.createDM();
          await dmChannel.send({
            content: `${EMOJIS.TRADE.ACTIVE} **Trade Activated**\n\nYour trade is now active! Funds have been locked in escrow.`,
            embeds: [embed],
            components: [buttons]
          });

          this.logOperation('Trade accepted DM sent', {
            tradeId: trade.tradeId,
            recipient: user.id
          });
        } catch (dmError) {
          this.logger.warn('Could not send trade accepted DM', {
            tradeId: trade.tradeId,
            recipient: user.id,
            error: dmError
          });
        }
      }

    } catch (error) {
      this.handleError(error, { operation: 'send_trade_accepted', tradeId: trade.tradeId });
    }
  }

  /**
   * Send trade completed notification
   */
  async sendTradeCompleted(trade: ITrade, client: Client): Promise<void> {
    this.logOperation('Sending trade completed notification', { tradeId: trade.tradeId });
    // Implementation will be added when we create the UI components
  }

  /**
   * Send partial confirmation notification
   */
  async sendPartialConfirmation(trade: ITrade, confirmingUserId: string, client: Client): Promise<void> {
    this.logOperation('Sending partial confirmation notification', { 
      tradeId: trade.tradeId, 
      confirmingUserId 
    });
    // Implementation will be added when we create the UI components
  }

  /**
   * Send trade cancelled notification
   */
  async sendTradeCancelled(trade: ITrade, cancellingUserId: string, reason: string | undefined, client: Client): Promise<void> {
    this.logOperation('Sending trade cancelled notification', { 
      tradeId: trade.tradeId, 
      cancellingUserId,
      reason 
    });
    // Implementation will be added when we create the UI components
  }

  /**
   * Send trade expired notification
   */
  async sendTradeExpired(trade: ITrade, client: Client): Promise<void> {
    this.logOperation('Sending trade expired notification', { tradeId: trade.tradeId });
    // Implementation will be added when we create the UI components
  }

  /**
   * Send trade warning notification
   */
  async sendTradeWarning(trade: ITrade, hoursRemaining: number, client: Client): Promise<void> {
    this.logOperation('Sending trade warning notification', { 
      tradeId: trade.tradeId, 
      hoursRemaining 
    });
    // Implementation will be added when we create the UI components
  }

  /**
   * Send dispute initiated notification
   */
  async sendDisputeInitiated(trade: ITrade, disputingUserId: string, client: Client): Promise<void> {
    this.logOperation('Sending dispute initiated notification', { 
      tradeId: trade.tradeId, 
      disputingUserId 
    });
    // Implementation will be added when we create the UI components
  }

  /**
   * Send dispute resolved notification
   */
  async sendDisputeResolved(trade: ITrade, resolution: string, client: Client): Promise<void> {
    this.logOperation('Sending dispute resolved notification', { 
      tradeId: trade.tradeId, 
      resolution 
    });
    // Implementation will be added when we create the UI components
  }
}
